<template>
  <HerbitProfessionalLayout
    title="Generate Session Codes"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="teal">
          <form @submit.prevent="generateSessions" class="space-y-6">
              <!-- Assessment Selection -->
              <div>
                <Label for="assessmentSelect" class="text-gray-300">Select Assessment</Label>
                <select
                  id="assessmentSelect"
                  name="assessmentSelect"
                  v-model="selectedAssessmentId"
                  autocomplete="off"
                  class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  required
                >
                  <option value="" disabled>Select an assessment</option>
                  <option v-for="assessment in assessments" :key="assessment.id" :value="assessment.id">
                    {{ assessment.id }}: {{ assessment.name }}
                  </option>
                </select>
              </div>

              <!-- Usernames -->
              <div>
                <Label for="usernames" class="text-gray-300">Usernames (comma-separated)</Label>
                <textarea
                  id="usernames"
                  name="usernames"
                  v-model="usernames"
                  autocomplete="off"
                  class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent h-24"
                  placeholder="e.g. user1,user2,user3"
                  required
                ></textarea>
                <p class="text-xs text-gray-400 mt-1">Enter usernames separated by commas (no spaces).</p>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500"></div>
                <span class="ml-3 text-gray-300">Generating session codes...</span>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Results table -->
              <div v-if="sessions.length > 0" class="space-y-4">

                <!-- Sessions table -->
                <div class="overflow-x-auto">
                  <table class="w-full text-left border-collapse">
                    <thead>
                      <tr class="border-b border-gray-700">
                        <th class="py-3 px-4 text-gray-300 font-medium">Username</th>
                        <th class="py-3 px-4 text-gray-300 font-medium">Session Code</th>
                        <th class="py-3 px-4 text-gray-300 font-medium">Session ID</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(session, index) in sessions" :key="index" class="border-b border-gray-800">
                        <td class="py-3 px-4 text-white">{{ session.username }}</td>
                        <td class="py-3 px-4">
                          <span class="font-mono bg-gray-800 text-cyan-400 px-2 py-1 rounded">{{ session.sessionCode }}</span>
                        </td>
                        <td class="py-3 px-4 text-gray-300">{{ session.sessionDbId }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- Generated Link Display -->
                <div v-if="generatedLink" class="mt-8 p-6 bg-gray-800/50 rounded-lg border border-teal-500/30">
                  <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    Generated Quiz Link
                  </h3>

                  <div class="bg-gray-900/70 p-4 rounded-lg border border-gray-700 mb-4">
                    <p class="text-gray-300 text-sm mb-2">Share this link with users to take the quiz:</p>
                    <div class="flex items-center space-x-2">
                      <input
                        type="text"
                        :value="generatedLink"
                        readonly
                        class="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                      />
                      <Button
                        @click="copyToClipboard"
                        variant="ghost"
                        size="sm"
                        class="flex-shrink-0 text-xs !text-gray-300 !bg-gray-700 !border !border-gray-600 hover:!border-gray-400 !shadow-none !ring-0"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Copy
                      </Button>
                    </div>
                  </div>

                  <div class="text-sm text-gray-400">
                    <p><strong>Assessment:</strong> {{ selectedAssessmentName }}</p>
                    <p><strong>Generated Sessions:</strong> {{ sessions.length }}</p>
                    <p><strong>Generated:</strong> {{ new Date().toLocaleString() }}</p>
                  </div>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="sessionGenerate"
                  size="skillButton"
                  :disabled="isLoading"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z" />
                    </svg>
                    Generate Codes
                  </span>
                </Button>
              </div>
            </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const selectedAssessmentId = ref('');
const usernames = ref('');
const assessments = ref([]);
const isLoading = ref(false);
const sessions = ref([]);
const generatedLink = ref(''); // Store the generated quiz link

// Computed properties
const selectedAssessment = computed(() => {
  return assessments.value.find(a => a.id == selectedAssessmentId.value);
});

const selectedAssessmentName = computed(() => {
  return selectedAssessment.value ? `${selectedAssessment.value.id}: ${selectedAssessment.value.name}` : '';
});

// Fetch assessments from API
const fetchAssessments = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getAssessments();
    assessments.value = response.data.assessments || [];
  } catch (error) {
    logError(error, 'fetchAssessments');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch assessments'));
  } finally {
    isLoading.value = false;
  }
};

// Generate sessions via API
const generateSessions = async () => {
  if (!selectedAssessmentId.value || !usernames.value) {
    setErrorMessage('Please fill in all required fields');
    return;
  }

  isLoading.value = true;
  clearMessage();
  sessions.value = [];
  generatedLink.value = '';

  try {
    // Validate usernames format
    const usernameList = usernames.value.split(',').map(username => username.trim()).filter(username => username);
    if (usernameList.length === 0) {
      throw new Error('Please enter at least one username');
    }

    // Additional validation for usernames
    const invalidUsernames = [];
    for (const username of usernameList) {
      if (username.length < 2 || username.length > 50) {
        invalidUsernames.push(username);
      } else if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
        invalidUsernames.push(username);
      }
    }

    if (invalidUsernames.length > 0) {
      throw new Error(`Invalid usernames: ${invalidUsernames.join(', ')}. Usernames must be 2-50 characters and contain only letters, numbers, underscore, dash, or dot.`);
    }

    // Check for duplicates
    const uniqueUsernames = [...new Set(usernameList)];
    if (uniqueUsernames.length !== usernameList.length) {
      throw new Error('Duplicate usernames are not allowed');
    }

    // Validate assessment ID
    const assessmentId = parseInt(selectedAssessmentId.value);
    if (isNaN(assessmentId) || assessmentId <= 0) {
      throw new Error('Invalid assessment selected');
    }

    // Call the API to generate sessions
    const response = await api.admin.createSession({
      assessment_id: assessmentId,
      usernames: usernames.value // API expects comma-separated string
    });

    // Validate response
    if (!response.data) {
      throw new Error('Invalid response from server');
    }

    sessions.value = response.data.sessions || [];

    // Handle warnings if any sessions failed
    let messageText = response.data.message || `Successfully generated ${sessions.value.length} session codes!`;
    if (response.data.warnings) {
      messageText += `\n\nWarnings: ${response.data.warnings}`;
    }

    setSuccessMessage(messageText);

    // Clear the form on successful generation
    usernames.value = '';

    // Generate quiz link automatically
    if (sessions.value.length > 0) {
      await generateQuizLink(assessmentId);
    }

  } catch (error) {
    logError(error, 'generateSessions');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while generating sessions'));
  } finally {
    isLoading.value = false;
  }
};

// Generate quiz link for the assessment
const generateQuizLink = async (assessmentId) => {
  try {
    const response = await api.admin.generateLink({
      assessment_id: assessmentId
    });

    generatedLink.value = response.data.link;
    setSuccessMessage('Sessions and quiz link generated successfully!');
  } catch (error) {
    logError(error, 'generateQuizLink');
    setErrorMessage(getErrorMessage(error, 'Sessions were created, but failed to generate quiz link'));
    generatedLink.value = '';
  }
};

// Copy link to clipboard
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(generatedLink.value);
    setSuccessMessage('Link copied to clipboard!');
  } catch (error) {
    logError(error, 'copyToClipboard');
    setErrorMessage('Failed to copy to clipboard');
  }
};

onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
